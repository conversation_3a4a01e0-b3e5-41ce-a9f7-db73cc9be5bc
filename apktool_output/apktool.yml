!!brut.androlib.meta.MetaInfo
apkFileName: app-release.apk
compressionType: false
doNotCompress:
- resources.arsc
- META-INF/androidx.activity_activity.version
- META-INF/androidx.annotation_annotation-experimental.version
- META-INF/androidx.appcompat_appcompat-resources.version
- META-INF/androidx.appcompat_appcompat.version
- META-INF/androidx.autofill_autofill.version
- META-INF/androidx.core_core-ktx.version
- META-INF/androidx.core_core.version
- META-INF/androidx.cursoradapter_cursoradapter.version
- META-INF/androidx.customview_customview.version
- META-INF/androidx.drawerlayout_drawerlayout.version
- META-INF/androidx.emoji2_emoji2-views-helper.version
- META-INF/androidx.emoji2_emoji2.version
- META-INF/androidx.fragment_fragment.version
- META-INF/androidx.interpolator_interpolator.version
- META-INF/androidx.loader_loader.version
- META-INF/androidx.profileinstaller_profileinstaller.version
- META-INF/androidx.savedstate_savedstate.version
- META-INF/androidx.startup_startup-runtime.version
- META-INF/androidx.swiperefreshlayout_swiperefreshlayout.version
- META-INF/androidx.tracing_tracing.version
- META-INF/androidx.vectordrawable_vectordrawable-animated.version
- META-INF/androidx.vectordrawable_vectordrawable.version
- META-INF/androidx.versionedparcelable_versionedparcelable.version
- META-INF/androidx.viewpager_viewpager.version
- META-INF/kotlinx_coroutines_android.version
- META-INF/kotlinx_coroutines_core.version
- assets/index.android.bundle
- assets/dexopt/baseline.prof
- assets/dexopt/baseline.profm
- lib/arm64-v8a/libappmodules.so
- lib/arm64-v8a/libc++_shared.so
- lib/arm64-v8a/libfbjni.so
- lib/arm64-v8a/libhermes.so
- lib/arm64-v8a/libhermestooling.so
- lib/arm64-v8a/libimagepipeline.so
- lib/arm64-v8a/libjsi.so
- lib/arm64-v8a/libnative-filters.so
- lib/arm64-v8a/libnative-imagetranscoder.so
- lib/arm64-v8a/libreactnative.so
- lib/armeabi-v7a/libappmodules.so
- lib/armeabi-v7a/libc++_shared.so
- lib/armeabi-v7a/libfbjni.so
- lib/armeabi-v7a/libhermes.so
- lib/armeabi-v7a/libhermestooling.so
- lib/armeabi-v7a/libimagepipeline.so
- lib/armeabi-v7a/libjsi.so
- lib/armeabi-v7a/libnative-filters.so
- lib/armeabi-v7a/libnative-imagetranscoder.so
- lib/armeabi-v7a/libreactnative.so
- lib/x86/libappmodules.so
- lib/x86/libc++_shared.so
- lib/x86/libfbjni.so
- lib/x86/libhermes.so
- lib/x86/libhermestooling.so
- lib/x86/libimagepipeline.so
- lib/x86/libjsi.so
- lib/x86/libnative-filters.so
- lib/x86/libnative-imagetranscoder.so
- lib/x86/libreactnative.so
- lib/x86_64/libappmodules.so
- lib/x86_64/libc++_shared.so
- lib/x86_64/libfbjni.so
- lib/x86_64/libhermes.so
- lib/x86_64/libhermestooling.so
- lib/x86_64/libimagepipeline.so
- lib/x86_64/libjsi.so
- lib/x86_64/libnative-filters.so
- lib/x86_64/libnative-imagetranscoder.so
- lib/x86_64/libreactnative.so
- png
isFrameworkApk: false
packageInfo:
  forcedPackageId: '127'
  renameManifestPackage: null
sdkInfo:
  minSdkVersion: '24'
  targetSdkVersion: '35'
sharedLibrary: false
sparseResources: false
unknownFiles:
  DebugProbesKt.bin: '8'
  kotlin-tooling-metadata.json: '8'
  okhttp3/internal/publicsuffix/NOTICE: '8'
  okhttp3/internal/publicsuffix/publicsuffixes.gz: '0'
usesFramework:
  ids:
  - 1
  tag: null
version: 2.7.0-dirty
versionInfo:
  versionCode: '1'
  versionName: '1.0'
