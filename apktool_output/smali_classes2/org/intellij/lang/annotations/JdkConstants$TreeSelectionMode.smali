.class public interface abstract annotation Lorg/intellij/lang/annotations/JdkConstants$TreeSelectionMode;
.super Ljava/lang/Object;
.source "JdkConstants.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/intellij/lang/annotations/JdkConstants;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2609
    name = "TreeSelectionMode"
.end annotation
