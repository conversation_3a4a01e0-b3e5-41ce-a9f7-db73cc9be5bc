.class public final Lokhttp3/internal/ws/MessageInflaterKt;
.super Ljava/lang/Object;
.source "MessageInflater.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    bv = {
        0x1,
        0x0,
        0x3
    }
    d1 = {
        "\u0000\u0008\n\u0000\n\u0002\u0010\u0008\n\u0000\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0002"
    }
    d2 = {
        "OCTETS_TO_ADD_BEFORE_INFLATION",
        "",
        "okhttp"
    }
    k = 0x2
    mv = {
        0x1,
        0x4,
        0x0
    }
.end annotation


# static fields
.field private static final OCTETS_TO_ADD_BEFORE_INFLATION:I = 0xffff
